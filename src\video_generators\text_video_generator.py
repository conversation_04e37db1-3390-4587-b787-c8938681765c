"""
Text-only video generator for creating Instagram reels from text messages
"""
import os
import random
from pathlib import Path
from typing import Optional, <PERSON><PERSON>
import textwrap

from PIL import Image, ImageDraw, ImageFont
import cv2
import numpy as np
from moviepy.editor import VideoFileClip, AudioFileClip, CompositeVideoClip, ImageClip
import arabic_reshaper
from bidi.algorithm import get_display

from src.config import Config
from src.utils.logger import logger
from src.utils.emoji_renderer import EmojiRenderer

class TextVideoGenerator:
    """Generates Instagram reel videos from text content"""
    
    def __init__(self):
        self.width = Config.VIDEO_WIDTH
        self.height = Config.VIDEO_HEIGHT
        self.duration = Config.VIDEO_DURATION_SECONDS
        self.fps = Config.FPS
        self.font_path = Config.FONT_PATH
        self.font_size = Config.FONT_SIZE
        
        # Twitter-like styling
        self.background_color = (21, 32, 43)  # Twitter dark blue
        self.text_color = (255, 255, 255)     # White text
        self.accent_color = (29, 161, 242)    # Twitter blue
        self.border_color = (56, 68, 77)      # Border color
        
        # Load font
        self._load_font()

        # Initialize emoji renderer
        self.emoji_renderer = EmojiRenderer(emoji_size=int(self.font_size * 0.9))
    
    def _load_font(self):
        """Load the Vazir font for Farsi text with emoji support"""
        try:
            if self.font_path.exists():
                self.font = ImageFont.truetype(str(self.font_path), self.font_size)
                self.small_font = ImageFont.truetype(str(self.font_path), int(self.font_size * 0.6))
                self.large_font = ImageFont.truetype(str(self.font_path), int(self.font_size * 1.2))
                logger.info(f"Loaded font: {self.font_path}")
            else:
                logger.warning(f"Font not found: {self.font_path}, using default")
                self.font = ImageFont.load_default()
                self.small_font = ImageFont.load_default()
                self.large_font = ImageFont.load_default()

            # Try to load emoji font as fallback
            self._load_emoji_font()

        except Exception as e:
            logger.error(f"Error loading font: {e}")
            self.font = ImageFont.load_default()
            self.small_font = ImageFont.load_default()
            self.large_font = ImageFont.load_default()

    def _load_emoji_font(self):
        """Load system emoji font as fallback"""
        try:
            import platform
            system = platform.system()

            emoji_font_paths = []
            if system == "Windows":
                emoji_font_paths = [
                    "C:/Windows/Fonts/seguiemj.ttf",  # Segoe UI Emoji
                    "C:/Windows/Fonts/segoeui.ttf",   # Segoe UI (has some emoji support)
                    "C:/Windows/Fonts/arial.ttf",     # Arial (fallback)
                ]
            elif system == "Darwin":  # macOS
                emoji_font_paths = [
                    "/System/Library/Fonts/Apple Color Emoji.ttc",
                    "/System/Library/Fonts/Helvetica.ttc"
                ]
            else:  # Linux
                emoji_font_paths = [
                    "/usr/share/fonts/truetype/noto/NotoColorEmoji.ttf",
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
                ]

            for font_path in emoji_font_paths:
                try:
                    if Path(font_path).exists():
                        # Try different sizes to find one that works
                        for size in [self.font_size, self.font_size - 4, self.font_size - 8]:
                            try:
                                self.emoji_font = ImageFont.truetype(font_path, size)
                                # Test if it can render an emoji
                                test_img = Image.new('RGB', (50, 50), 'white')
                                test_draw = ImageDraw.Draw(test_img)
                                test_draw.text((10, 10), '😊', font=self.emoji_font, fill='black')
                                logger.info(f"Loaded emoji font: {font_path} (size: {size})")
                                return
                            except:
                                continue
                except:
                    continue

            self.emoji_font = None
            logger.warning("No working emoji font found, will use text fallback")

        except Exception as e:
            logger.warning(f"Could not load emoji font: {e}")
            self.emoji_font = None
    
    def _prepare_farsi_text(self, text: str) -> str:
        """Prepare Farsi text for proper display"""
        try:
            # Reshape Arabic/Farsi text
            reshaped_text = arabic_reshaper.reshape(text)
            # Apply bidirectional algorithm
            display_text = get_display(reshaped_text)
            return display_text
        except Exception as e:
            logger.warning(f"Error preparing Farsi text: {e}")
            return text

    def _prepare_farsi_lines(self, lines: list) -> list:
        """Prepare multiple lines of Farsi text with correct ordering"""
        try:
            prepared_lines = []

            # First, check if we have Farsi text
            has_farsi = any(self._contains_farsi_chars(line) for line in lines)

            if has_farsi:
                # For Farsi text, we need to:
                # 1. Keep the original line order (don't reverse)
                # 2. Process each line for proper character shaping
                for line in lines:
                    reshaped_line = arabic_reshaper.reshape(line)
                    display_line = get_display(reshaped_line)
                    prepared_lines.append(display_line)

                # The key insight: DON'T reverse the lines!
                # The original text wrapping already has the correct logical order
                return prepared_lines
            else:
                # For non-Farsi text, just return as-is
                return lines

        except Exception as e:
            logger.warning(f"Error preparing Farsi lines: {e}")
            return lines

    def _contains_farsi_chars(self, text: str) -> bool:
        """Check if text contains Farsi/Arabic characters"""
        farsi_range = range(0x0600, 0x06FF + 1)  # Arabic/Farsi Unicode range
        return any(ord(char) in farsi_range for char in text)
    
    def _wrap_text(self, text: str, max_width: int) -> list:
        """Wrap text to fit within specified width with proper Farsi handling"""

        # Check if text contains Farsi characters
        has_farsi = self._contains_farsi_chars(text)

        if has_farsi:
            # For Farsi text, we need special handling
            return self._wrap_farsi_text(text, max_width)
        else:
            # For non-Farsi text, use simple wrapping with emoji-aware width calculation
            words = text.split()
            lines = []
            current_line = []

            for word in words:
                test_line = ' '.join(current_line + [word])
                # Use emoji-aware width calculation
                text_width = self._calculate_text_width_with_emojis(test_line, self.font)

                if text_width <= max_width:
                    current_line.append(word)
                else:
                    if current_line:
                        lines.append(' '.join(current_line))
                        current_line = [word]
                    else:
                        lines.append(word)

            if current_line:
                lines.append(' '.join(current_line))

            return lines

    def _calculate_optimal_font_and_layout(self, text: str, max_width: int, initial_max_height: int) -> tuple:
        """Calculate optimal font size and required height for text layout"""
        # Use a good readable font size - don't go too small
        preferred_font_size = min(self.font_size, 38)  # Good readable size
        min_font_size = 24  # Minimum readable size - larger than before

        # Try preferred font size first
        for test_font_size in [preferred_font_size, preferred_font_size - 4, preferred_font_size - 8, min_font_size]:
            try:
                test_font = ImageFont.truetype(str(self.font_path), test_font_size)

                # Test wrap with this font size
                lines = self._wrap_text_with_font(text, max_width, test_font)

                # Check if individual lines fit width using emoji-aware calculation
                fits_width = True
                for line in lines:
                    try:
                        line_width = self._calculate_text_width_with_emojis(line, test_font)
                        if line_width > max_width:
                            fits_width = False
                            break
                    except:
                        fits_width = False
                        break

                if fits_width and len(lines) <= 12:  # Allow up to 12 lines
                    # Calculate required height
                    line_height = int(test_font_size * 1.4)
                    required_height = len(lines) * line_height + 60  # Extra padding

                    return test_font_size, required_height, lines

            except Exception:
                continue

        # Fallback
        return min_font_size, initial_max_height, []

    def _wrap_text_with_font(self, text: str, max_width: int, font) -> list:
        """Wrap text using specific font"""
        has_farsi = self._contains_farsi_chars(text)

        if has_farsi:
            return self._wrap_farsi_text_with_font(text, max_width, font)
        else:
            # English text wrapping with emoji-aware width calculation
            words = text.split()
            lines = []
            current_line = []

            for word in words:
                test_line = ' '.join(current_line + [word])

                try:
                    # Use emoji-aware width calculation
                    text_width = self._calculate_text_width_with_emojis(test_line, font)
                except:
                    # If we can't measure, estimate based on character count
                    text_width = len(test_line) * (font.size * 0.6)

                if text_width <= max_width:
                    current_line.append(word)
                else:
                    if current_line:
                        lines.append(' '.join(current_line))
                        current_line = [word]
                    else:
                        # Single word is too long, but add it anyway
                        lines.append(word)
                        current_line = []

            if current_line:
                lines.append(' '.join(current_line))

            return lines if lines else [text]

    def _wrap_farsi_text(self, text: str, max_width: int) -> list:
        """Special wrapping for Farsi text to maintain correct line order"""
        try:
            import re

            # Simple and direct approach: split by exclamation mark first
            if '!' in text:
                parts = text.split('!')
                if len(parts) == 2:
                    # We have exactly one exclamation mark - perfect for our case
                    line1 = parts[0].strip() + '!'  # First sentence with exclamation
                    line2 = parts[1].strip()        # Second sentence

                    if line1 and line2:
                        # Process each line
                        reshaped1 = arabic_reshaper.reshape(line1)
                        display1 = get_display(reshaped1)
                        reshaped2 = arabic_reshaper.reshape(line2)
                        display2 = get_display(reshaped2)

                        return [display1, display2]

            # Fallback: look for other sentence boundaries
            sentence_endings = re.split(r'([.؟!])\s*', text)
            sentences = []
            current = ""

            for i, part in enumerate(sentence_endings):
                if part in ['.', '؟', '!']:
                    current += part
                    if current.strip():
                        sentences.append(current.strip())
                    current = ""
                else:
                    current += part

            if current.strip():
                sentences.append(current.strip())

            # If we have exactly 2 sentences, use them
            if len(sentences) == 2:
                reshaped1 = arabic_reshaper.reshape(sentences[0])
                display1 = get_display(reshaped1)
                reshaped2 = arabic_reshaper.reshape(sentences[1])
                display2 = get_display(reshaped2)
                return [display1, display2]

            # Final fallback: split text in half at word boundary
            words = text.split()
            if len(words) >= 4:
                # Find a good split point (around middle, but at word boundary)
                mid = len(words) // 2

                # Look for a good break point near the middle
                for i in range(max(1, mid-2), min(len(words)-1, mid+3)):
                    word = words[i-1]
                    if word.endswith(('!', '.', '؟')) or words[i] in ['این', 'که', 'و']:
                        mid = i
                        break

                line1 = ' '.join(words[:mid])
                line2 = ' '.join(words[mid:])

                reshaped1 = arabic_reshaper.reshape(line1)
                display1 = get_display(reshaped1)
                reshaped2 = arabic_reshaper.reshape(line2)
                display2 = get_display(reshaped2)

                return [display1, display2]

            # If all else fails, return single line
            reshaped = arabic_reshaper.reshape(text)
            display = get_display(reshaped)
            return [display]

        except Exception as e:
            logger.error(f"Error wrapping Farsi text: {e}")
            try:
                # Last resort: just reshape the whole text
                reshaped = arabic_reshaper.reshape(text)
                display = get_display(reshaped)
                return [display]
            except:
                return [text]

    def _wrap_farsi_text_with_font(self, text: str, max_width: int, font) -> list:
        """Wrap Farsi text using specific font with proper width checking"""
        try:
            import re

            # First, try to fit the entire text on one line
            reshaped_full = arabic_reshaper.reshape(text)
            display_full = get_display(reshaped_full)
            # Use emoji-aware width calculation
            full_width = self._calculate_text_width_with_emojis(display_full, font)

            if full_width <= max_width:
                return [display_full]

            # Text is too wide, need to wrap
            words = text.split()
            lines = []
            current_line_words = []

            for word in words:
                # Test adding this word to current line
                test_line_words = current_line_words + [word]
                test_line_text = ' '.join(test_line_words)

                # Reshape and measure the test line with emoji-aware calculation
                reshaped_test = arabic_reshaper.reshape(test_line_text)
                display_test = get_display(reshaped_test)
                test_width = self._calculate_text_width_with_emojis(display_test, font)

                if test_width <= max_width:
                    # Word fits, add it to current line
                    current_line_words.append(word)
                else:
                    # Word doesn't fit, finish current line and start new one
                    if current_line_words:
                        # Finish current line
                        current_line_text = ' '.join(current_line_words)
                        reshaped_line = arabic_reshaper.reshape(current_line_text)
                        display_line = get_display(reshaped_line)
                        lines.append(display_line)

                        # Start new line with current word
                        current_line_words = [word]
                    else:
                        # Single word is too long, add it anyway
                        reshaped_word = arabic_reshaper.reshape(word)
                        display_word = get_display(reshaped_word)
                        lines.append(display_word)
                        current_line_words = []

            # Add remaining words as final line
            if current_line_words:
                current_line_text = ' '.join(current_line_words)
                reshaped_line = arabic_reshaper.reshape(current_line_text)
                display_line = get_display(reshaped_line)
                lines.append(display_line)

            return lines if lines else [display_full]

        except Exception as e:
            logger.error(f"Error wrapping Farsi text with font: {e}")
            # Fallback: try simple split
            try:
                words = text.split()
                if len(words) > 2:
                    mid = len(words) // 2
                    line1 = ' '.join(words[:mid])
                    line2 = ' '.join(words[mid:])

                    reshaped1 = arabic_reshaper.reshape(line1)
                    display1 = get_display(reshaped1)
                    reshaped2 = arabic_reshaper.reshape(line2)
                    display2 = get_display(reshaped2)

                    return [display1, display2]
                else:
                    reshaped = arabic_reshaper.reshape(text)
                    display = get_display(reshaped)
                    return [display]
            except:
                return [text]

    def _draw_text_with_emoji(self, draw, position, text, font, fill):
        """Draw text with proper emoji support using emoji renderer"""
        try:
            # Use the emoji renderer to handle emojis properly
            success = self.emoji_renderer.render_text_with_emojis(draw, position, text, font, fill)

            if success:
                return

            # Fallback: clean text and draw normally
            clean_text = self._clean_problematic_chars(text)
            draw.text(position, clean_text, font=font, fill=fill)

        except Exception as e:
            logger.warning(f"Error drawing text: {e}")
            # Last resort: draw simplified text
            try:
                # Remove all non-basic characters
                simple_text = ''.join(c for c in text if ord(c) < 128 or self._is_farsi_char(c))
                draw.text(position, simple_text, font=font, fill=fill)
            except:
                draw.text(position, "Text rendering error", font=font, fill=fill)

    def _is_farsi_char(self, char):
        """Check if a character is a Farsi/Arabic character"""
        code = ord(char)
        return (0x0600 <= code <= 0x06FF or  # Arabic
                0x0750 <= code <= 0x077F or  # Arabic Supplement
                0x08A0 <= code <= 0x08FF or  # Arabic Extended-A
                0xFB50 <= code <= 0xFDFF or  # Arabic Presentation Forms-A
                0xFE70 <= code <= 0xFEFF)    # Arabic Presentation Forms-B

    def _clean_problematic_chars(self, text: str) -> str:
        """Clean problematic characters but keep emojis for proper rendering"""
        try:
            import re

            # Keep most characters including emojis, only remove truly problematic ones
            cleaned_chars = []

            for char in text:
                code = ord(char)

                # Keep basic ASCII printable characters
                if 32 <= code <= 126:
                    cleaned_chars.append(char)
                # Keep Farsi/Arabic characters
                elif self._is_farsi_char(char):
                    cleaned_chars.append(char)
                # Keep emojis (we'll handle them specially in drawing)
                elif self._is_emoji(char):
                    cleaned_chars.append(char)
                # Keep common Unicode punctuation and symbols
                elif char in '!@#$%^&*()[]{}|\\:";\'<>?,./-_=+`~':
                    cleaned_chars.append(char)
                # Replace control characters and other problematic chars with space
                elif code < 32 or (127 <= code <= 159):  # Control characters
                    cleaned_chars.append(' ')
                # Keep other printable Unicode characters
                elif char.isprintable():
                    cleaned_chars.append(char)
                else:
                    cleaned_chars.append(' ')

            # Join and clean up spaces
            cleaned = ''.join(cleaned_chars)
            cleaned = re.sub(r'\s+', ' ', cleaned).strip()

            return cleaned

        except Exception as e:
            logger.warning(f"Error cleaning text: {e}")
            # Fallback: keep basic characters and emojis
            return ''.join(char for char in text if ord(char) < 128 or self._is_farsi_char(char) or self._is_emoji(char))

    def _is_emoji(self, char):
        """Check if a character is an emoji"""
        code = ord(char)
        return (
            0x1F600 <= code <= 0x1F64F or  # Emoticons
            0x1F300 <= code <= 0x1F5FF or  # Misc Symbols and Pictographs
            0x1F680 <= code <= 0x1F6FF or  # Transport and Map Symbols
            0x1F1E0 <= code <= 0x1F1FF or  # Regional Indicator Symbols
            0x2600 <= code <= 0x26FF or    # Misc symbols
            0x2700 <= code <= 0x27BF or    # Dingbats
            0xFE00 <= code <= 0xFE0F or    # Variation Selectors
            0x1F900 <= code <= 0x1F9FF      # Supplemental Symbols and Pictographs
        )

    def _calculate_text_width_with_emojis(self, text: str, font) -> int:
        """Calculate the visual width of text including proper emoji width"""
        try:
            import emoji as emoji_lib

            total_width = 0
            current_text_part = ""

            # Process character by character
            for char in text:
                if emoji_lib.is_emoji(char) or self._is_emoji(char):
                    # First, add width of any accumulated text
                    if current_text_part:
                        try:
                            bbox = font.getbbox(current_text_part)
                            total_width += bbox[2] - bbox[0]
                        except:
                            # Fallback calculation
                            total_width += len(current_text_part) * (font.size * 0.6)
                        current_text_part = ""

                    # Add emoji width (use emoji renderer size)
                    total_width += self.emoji_renderer.emoji_size
                else:
                    # Regular character, add to text part
                    current_text_part += char

            # Add width of any remaining text
            if current_text_part:
                try:
                    bbox = font.getbbox(current_text_part)
                    total_width += bbox[2] - bbox[0]
                except:
                    # Fallback calculation
                    total_width += len(current_text_part) * (font.size * 0.6)

            return total_width

        except Exception as e:
            logger.warning(f"Error calculating text width with emojis: {e}")
            # Fallback to regular font measurement
            try:
                bbox = font.getbbox(text)
                return bbox[2] - bbox[0]
            except:
                return len(text) * (font.size * 0.6)

    def _create_twitter_like_frame(self, text: str) -> Image.Image:
        """Create an Instagram-like frame with enhanced design"""
        # Create base image with gradient background
        img = Image.new('RGB', (self.width, self.height), self.background_color)

        # Try to use a background image with better blending
        bg_image_path = self._get_random_background_image()
        if bg_image_path:
            try:
                bg_img = Image.open(bg_image_path).resize((self.width, self.height), Image.Resampling.LANCZOS)
                # Create a more sophisticated overlay with gradient
                overlay = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
                overlay_draw = ImageDraw.Draw(overlay)

                # Create gradient overlay for better text readability
                for y in range(self.height):
                    alpha = int(120 + (y / self.height) * 60)  # Gradient from 120 to 180
                    overlay_draw.line([(0, y), (self.width, y)], fill=self.background_color + (alpha,))

                bg_img = bg_img.convert('RGBA')
                img = Image.alpha_composite(bg_img, overlay).convert('RGB')
            except Exception as e:
                logger.warning(f"Could not load background image: {e}")
                # Create gradient background as fallback
                for y in range(self.height):
                    color_factor = y / self.height
                    r = int(self.background_color[0] * (1 - color_factor * 0.3))
                    g = int(self.background_color[1] * (1 - color_factor * 0.3))
                    b = int(self.background_color[2] * (1 - color_factor * 0.3))
                    ImageDraw.Draw(img).line([(0, y), (self.width, y)], fill=(r, g, b))
        else:
            # Create gradient background
            for y in range(self.height):
                color_factor = y / self.height
                r = int(self.background_color[0] * (1 - color_factor * 0.3))
                g = int(self.background_color[1] * (1 - color_factor * 0.3))
                b = int(self.background_color[2] * (1 - color_factor * 0.3))
                ImageDraw.Draw(img).line([(0, y), (self.width, y)], fill=(r, g, b))

        draw = ImageDraw.Draw(img)
        
        # Calculate content area (leaving margins)
        margin = 80
        content_width = self.width - (2 * margin)
        content_height = self.height - (2 * margin)
        
        # Create Instagram-like container with modern design
        container_margin = 50
        container_padding = 80
        container_width = content_width - (2 * container_margin)
        max_container_height = content_height - 120  # More space for content

        # Calculate available text area with better proportions
        text_area_width = container_width - (2 * container_padding)
        text_area_height = max_container_height - 160  # Space for header/footer and padding

        # Calculate optimal font size and required height
        optimal_font_size, required_text_height, lines = self._calculate_optimal_font_and_layout(
            text, text_area_width, text_area_height
        )

        # Create responsive font with better sizing
        responsive_font = ImageFont.truetype(str(self.font_path), optimal_font_size)

        # If we didn't get lines from the calculation, wrap them now
        if not lines:
            lines = self._wrap_text_with_font(text, text_area_width, responsive_font)

        # Calculate actual text height with improved line spacing
        line_height = int(optimal_font_size * 1.5)  # Better line spacing
        actual_text_height = len(lines) * line_height

        # Container height with better proportions
        min_container_height = 300  # Larger minimum for better presence
        container_height = max(
            min_container_height,
            actual_text_height + (2 * container_padding) + 160  # More generous spacing
        )

        # Allow container to be taller than screen if needed (will be scrollable in video)
        if container_height > max_container_height:
            container_height = min(container_height, self.height - 100)  # Leave some margin
        
        # Center the container vertically
        container_y = (self.height - container_height) // 2
        container_x = margin + container_margin

        # Draw Instagram-like container with enhanced shadow and styling
        shadow_offset = 12

        # Create shadow effect
        from PIL import ImageFilter
        shadow_img = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
        shadow_draw = ImageDraw.Draw(shadow_img)

        # Create layered shadow effect
        for i in range(3):
            offset = shadow_offset - (i * 3)
            alpha = 60 - (i * 15)
            shadow_draw.rounded_rectangle(
                [container_x + offset, container_y + offset,
                 container_x + container_width + offset, container_y + container_height + offset],
                radius=30,
                fill=(0, 0, 0, alpha)
            )

        # Apply shadow with blur
        shadow_img = shadow_img.filter(ImageFilter.GaussianBlur(radius=6))
        img = img.convert('RGBA')
        img = Image.alpha_composite(img, shadow_img).convert('RGB')

        # Draw main container with gradient background
        draw = ImageDraw.Draw(img)

        # Create gradient for container background
        container_bg = Image.new('RGBA', (container_width, container_height), (0, 0, 0, 0))
        container_draw = ImageDraw.Draw(container_bg)

        for y in range(container_height):
            factor = y / container_height
            r = int(40 + factor * 15)  # Gradient from dark to slightly lighter
            g = int(50 + factor * 15)
            b = int(65 + factor * 15)
            container_draw.line([(0, y), (container_width, y)], fill=(r, g, b, 240))

        # Create rounded rectangle mask
        mask = Image.new('L', (container_width, container_height), 0)
        mask_draw = ImageDraw.Draw(mask)
        mask_draw.rounded_rectangle([0, 0, container_width, container_height], radius=30, fill=255)

        # Apply mask to gradient background
        container_bg.putalpha(mask)

        # Paste gradient container onto main image
        img_rgba = img.convert('RGBA')
        img_rgba.paste(container_bg, (container_x, container_y), container_bg)
        img = img_rgba.convert('RGB')

        # Add subtle border
        draw = ImageDraw.Draw(img)
        draw.rounded_rectangle(
            [container_x, container_y, container_x + container_width, container_y + container_height],
            radius=30,
            outline=(80, 90, 105),  # Subtle border
            width=2
        )
        
        # Draw profile section with enhanced styling
        profile_y = container_y + container_padding

        # Profile picture with gradient and shadow
        profile_pic_size = 60
        profile_pic_x = container_x + container_padding

        # Create profile picture with gradient
        profile_gradient = Image.new('RGBA', (profile_pic_size, profile_pic_size), (0, 0, 0, 0))
        profile_draw = ImageDraw.Draw(profile_gradient)

        # Create circular gradient for profile picture
        center = profile_pic_size // 2
        for r in range(center):
            factor = r / center
            color_r = int(self.accent_color[0] * (1 - factor * 0.3))
            color_g = int(self.accent_color[1] * (1 - factor * 0.3))
            color_b = int(self.accent_color[2] * (1 - factor * 0.3))
            profile_draw.ellipse(
                [center - r, center - r, center + r, center + r],
                fill=(color_r, color_g, color_b, 255)
            )

        # Paste profile picture
        img_rgba = img.convert('RGBA')
        img_rgba.paste(profile_gradient, (profile_pic_x, profile_y), profile_gradient)
        img = img_rgba.convert('RGB')
        draw = ImageDraw.Draw(img)

        # Channel name and handle with better typography
        name_x = profile_pic_x + profile_pic_size + 20
        draw.text((name_x, profile_y + 5), Config.CHANNEL_NAME, font=self.small_font, fill=self.text_color)
        draw.text((name_x, profile_y + 30), f"@{Config.INSTAGRAM_HANDLE}", font=self.small_font, fill=(136, 153, 166))
        
        # Main text with responsive font and better spacing
        text_y = profile_y + profile_pic_size + 40

        # Calculate the safe text area bounds
        text_area_left = container_x + container_padding
        text_area_right = container_x + container_width - container_padding
        text_area_width_safe = text_area_right - text_area_left

        for i, line in enumerate(lines):
            y_pos = text_y + (i * line_height)

            # Calculate text width with emoji awareness
            try:
                text_width = self._calculate_text_width_with_emojis(line, responsive_font)
            except:
                # Fallback width calculation
                text_width = len(line) * (optimal_font_size * 0.6)

            # Ensure text fits within safe area
            if text_width > text_area_width_safe:
                # Text is too wide, align to left edge of safe area
                x_pos = text_area_left
            else:
                # Center text within safe area
                x_pos = text_area_left + (text_area_width_safe - text_width) // 2

            # Final bounds check
            if x_pos < text_area_left:
                x_pos = text_area_left
            elif x_pos + text_width > text_area_right:
                x_pos = text_area_right - text_width
                if x_pos < text_area_left:  # Text is still too wide
                    x_pos = text_area_left

            # Draw text with subtle shadow for better readability
            shadow_offset = 2
            # Draw shadow
            try:
                self._draw_text_with_emoji(draw, (x_pos + shadow_offset, y_pos + shadow_offset), line, responsive_font, (0, 0, 0, 100))
            except:
                draw.text((x_pos + shadow_offset, y_pos + shadow_offset), line, font=responsive_font, fill=(0, 0, 0, 100))

            # Draw main text with emoji support
            self._draw_text_with_emoji(draw, (x_pos, y_pos), line, responsive_font, self.text_color)
        
        # Add engagement icons at bottom (like, retweet, share)
        icons_y = container_y + container_height - container_padding - 30
        icon_spacing = container_width // 4

        # Enhanced icon representations with gradients
        for i in range(3):
            icon_x = container_x + container_padding + (i * icon_spacing)

            # Create gradient for icons
            icon_gradient = Image.new('RGBA', (20, 20), (0, 0, 0, 0))
            icon_draw = ImageDraw.Draw(icon_gradient)

            # Different colors for different icons
            colors = [(255, 91, 109), (29, 161, 242), (23, 191, 99)]  # Like, retweet, share colors
            color = colors[i % 3]

            for r in range(10):
                factor = r / 10
                r_val = int(color[0] * (1 - factor * 0.3))
                g_val = int(color[1] * (1 - factor * 0.3))
                b_val = int(color[2] * (1 - factor * 0.3))
                icon_draw.ellipse([10 - r, 10 - r, 10 + r, 10 + r], fill=(r_val, g_val, b_val, 180))

            # Paste icon
            img_rgba = img.convert('RGBA')
            img_rgba.paste(icon_gradient, (icon_x, icons_y), icon_gradient)
            img = img_rgba.convert('RGB')
            draw = ImageDraw.Draw(img)
        
        # Add logo with better positioning and styling
        logo_path = self._get_brand_logo("small")
        if logo_path:
            try:
                logo = Image.open(logo_path).resize((70, 70), Image.Resampling.LANCZOS)
                logo_x = self.width - 90
                logo_y = 30

                # Add subtle shadow for logo
                if logo.mode == 'RGBA':
                    # Create shadow
                    shadow = Image.new('RGBA', (logo.width + 4, logo.height + 4), (0, 0, 0, 0))
                    shadow_draw = ImageDraw.Draw(shadow)
                    shadow_draw.ellipse([2, 2, logo.width + 2, logo.height + 2], fill=(0, 0, 0, 50))

                    img_rgba = img.convert('RGBA')
                    img_rgba.paste(shadow, (logo_x - 2, logo_y + 2), shadow)
                    img_rgba.paste(logo, (logo_x, logo_y), logo)
                    img = img_rgba.convert('RGB')
                else:
                    img.paste(logo, (logo_x, logo_y))
            except Exception as e:
                logger.warning(f"Could not add logo: {e}")

        # Add branding text with enhanced styling
        brand_text = f"@{Config.CHANNEL_NAME}"
        brand_bbox = self.small_font.getbbox(brand_text)
        brand_width = brand_bbox[2] - brand_bbox[0]
        brand_x = self.width - brand_width - 40
        brand_y = self.height - 60

        # Add gradient background for brand text
        brand_bg = Image.new('RGBA', (brand_width + 30, 35), (0, 0, 0, 0))
        brand_bg_draw = ImageDraw.Draw(brand_bg)

        # Create gradient background
        for y in range(35):
            factor = y / 35
            r = int(self.accent_color[0] * (1 - factor * 0.2))
            g = int(self.accent_color[1] * (1 - factor * 0.2))
            b = int(self.accent_color[2] * (1 - factor * 0.2))
            brand_bg_draw.line([(0, y), (brand_width + 30, y)], fill=(r, g, b, 200))

        # Create rounded rectangle mask for brand background
        brand_mask = Image.new('L', (brand_width + 30, 35), 0)
        brand_mask_draw = ImageDraw.Draw(brand_mask)
        brand_mask_draw.rounded_rectangle([0, 0, brand_width + 30, 35], radius=15, fill=255)
        brand_bg.putalpha(brand_mask)

        # Paste brand background
        img_rgba = img.convert('RGBA')
        img_rgba.paste(brand_bg, (brand_x - 15, brand_y - 5), brand_bg)
        img = img_rgba.convert('RGB')

        # Draw brand text with shadow
        draw = ImageDraw.Draw(img)
        draw.text((brand_x + 1, brand_y + 1), brand_text, font=self.small_font, fill=(0, 0, 0, 100))  # Shadow
        draw.text((brand_x, brand_y), brand_text, font=self.small_font, fill=(255, 255, 255))

        return img
    
    def _get_random_background_music(self) -> Optional[Path]:
        """Get a random background music file"""
        try:
            audio_files = list(Config.AUDIO_DIR.glob("*.mp3")) + list(Config.AUDIO_DIR.glob("*.wav"))
            if audio_files:
                return random.choice(audio_files)
            else:
                logger.warning("No audio files found in audio directory")
                return None
        except Exception as e:
            logger.error(f"Error getting background music: {e}")
            return None

    def _get_random_background_image(self) -> Optional[Path]:
        """Get a random background image"""
        try:
            bg_dir = Config.BASE_DIR / "assets" / "backgrounds"
            if bg_dir.exists():
                bg_files = list(bg_dir.glob("*.png")) + list(bg_dir.glob("*.jpg"))
                if bg_files:
                    return random.choice(bg_files)
        except Exception as e:
            logger.warning(f"Could not get background image: {e}")
        return None

    def _get_brand_logo(self, size: str = "small") -> Optional[Path]:
        """Get the brand logo"""
        try:
            logo_path = Config.BASE_DIR / "assets" / "logos" / f"linky_logo_{size}.png"
            if logo_path.exists():
                return logo_path
        except Exception as e:
            logger.warning(f"Could not get logo: {e}")
        return None
    
    def generate_video(self, text: str, output_path: Path) -> bool:
        """
        Generate a video from text content
        
        Args:
            text: The text content to convert to video
            output_path: Path where the video should be saved
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Generating text video: {output_path}")
            
            # Create the frame
            frame = self._create_twitter_like_frame(text)
            
            # Save frame as temporary image
            temp_image_path = Config.TEMP_DIR / f"temp_frame_{random.randint(1000, 9999)}.png"
            frame.save(temp_image_path)
            
            # Create video clip from image
            video_clip = ImageClip(str(temp_image_path)).set_duration(self.duration)
            
            # Add background music if available
            background_music = self._get_random_background_music()
            if background_music:
                try:
                    audio_clip = AudioFileClip(str(background_music))
                    # Loop audio if it's shorter than video
                    if audio_clip.duration < self.duration:
                        audio_clip = audio_clip.loop(duration=self.duration)
                    else:
                        audio_clip = audio_clip.subclip(0, self.duration)
                    
                    # Reduce volume to 30% for background music
                    audio_clip = audio_clip.volumex(0.3)
                    video_clip = video_clip.set_audio(audio_clip)
                    
                except Exception as e:
                    logger.warning(f"Could not add background music: {e}")
            
            # Write video file with optimized settings
            video_clip.write_videofile(
                str(output_path),
                fps=self.fps,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile=str(Config.TEMP_DIR / 'temp_audio.m4a'),
                remove_temp=True,
                verbose=False,
                logger=None,
                # Optimization settings for faster encoding
                preset='ultrafast',  # Fastest encoding preset
                ffmpeg_params=['-crf', '23', '-threads', '4']  # Good quality, multi-threading
            )
            
            # Clean up
            video_clip.close()
            if background_music and 'audio_clip' in locals():
                audio_clip.close()
            
            # Remove temporary image
            if temp_image_path.exists():
                temp_image_path.unlink()
            
            logger.info(f"Successfully generated text video: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error generating text video: {e}")
            return False
