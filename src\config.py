"""
Configuration module for LinkInsta automation app
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Application configuration"""
    
    # Base paths
    BASE_DIR = Path(__file__).parent.parent
    SRC_DIR = BASE_DIR / "src"
    AUDIO_DIR = BASE_DIR / "audio"
    FONTS_DIR = BASE_DIR / "fonts"
    OUTPUT_DIR = BASE_DIR / "output_videos"
    TEMP_DIR = BASE_DIR / "temp"
    LOGS_DIR = BASE_DIR / "logs"
    
    # Telegram settings
    TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
    TELEGRAM_CHAT_ID = int(os.getenv("TELEGRAM_CHAT_ID", "-1001069766340"))  # Channel for reading messages
    TELEGRAM_CHANNEL_USERNAME = os.getenv("TELEGRAM_CHANNEL_USERNAME", "@linkychannel")
    TELEGRAM_PERSONAL_CHAT_ID = int(os.getenv("TELEGRAM_PERSONAL_CHAT_ID", "142183523"))  # Your personal chat for videos
    
    # Instagram settings
    INSTAGRAM_USERNAME = os.getenv("INSTAGRAM_USERNAME")
    INSTAGRAM_PASSWORD = os.getenv("INSTAGRAM_PASSWORD")
    INSTAGRAM_SESSION_FILE = BASE_DIR / os.getenv("INSTAGRAM_SESSION_FILE", "instagram_session.json")
    
    # Application settings
    POLL_INTERVAL_MINUTES = int(os.getenv("POLL_INTERVAL_MINUTES", "30"))
    MAX_MESSAGES_TO_PROCESS = int(os.getenv("MAX_MESSAGES_TO_PROCESS", "5"))
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    DEMO_MODE = os.getenv("DEMO_MODE", "false").lower() == "true"

    # Feature toggles
    ENABLE_INSTAGRAM_POSTING = os.getenv("ENABLE_INSTAGRAM_POSTING", "false").lower() == "true"
    ENABLE_TELEGRAM_VIDEO_SENDING = os.getenv("ENABLE_TELEGRAM_VIDEO_SENDING", "false").lower() == "true"
    
    # Video settings
    VIDEO_WIDTH = int(os.getenv("VIDEO_WIDTH", "1080"))
    VIDEO_HEIGHT = int(os.getenv("VIDEO_HEIGHT", "1920"))
    VIDEO_DURATION_SECONDS = int(os.getenv("VIDEO_DURATION_SECONDS", "10"))
    FPS = int(os.getenv("FPS", "30"))
    
    # Font settings
    FONT_PATH = BASE_DIR / os.getenv("FONT_PATH", "fonts/Vazir-Regular.ttf")
    FONT_SIZE = int(os.getenv("FONT_SIZE", "48"))
    TEXT_COLOR = os.getenv("TEXT_COLOR", "white")
    BACKGROUND_COLOR = os.getenv("BACKGROUND_COLOR", "black")
    
    # Scheduling settings
    MIN_POST_INTERVAL_HOURS = int(os.getenv("MIN_POST_INTERVAL_HOURS", "2"))
    MAX_POST_INTERVAL_HOURS = int(os.getenv("MAX_POST_INTERVAL_HOURS", "8"))
    POSTING_START_HOUR = int(os.getenv("POSTING_START_HOUR", "8"))
    POSTING_END_HOUR = int(os.getenv("POSTING_END_HOUR", "22"))
    
    # V2ray config detection
    V2RAY_CONFIG_START_TEXT = "کانفیگ رایگان شما"
    
    # Instagram branding
    INSTAGRAM_HANDLE = "@linkychannell"
    CHANNEL_NAME = "linkychannell"
    
    @classmethod
    def create_directories(cls):
        """Create necessary directories if they don't exist"""
        for directory in [cls.AUDIO_DIR, cls.FONTS_DIR, cls.OUTPUT_DIR, 
                         cls.TEMP_DIR, cls.LOGS_DIR]:
            directory.mkdir(exist_ok=True)
    
    @classmethod
    def validate_config(cls):
        """Validate required configuration"""
        required_vars = [
            ("TELEGRAM_BOT_TOKEN", cls.TELEGRAM_BOT_TOKEN),
            ("INSTAGRAM_USERNAME", cls.INSTAGRAM_USERNAME),
            ("INSTAGRAM_PASSWORD", cls.INSTAGRAM_PASSWORD),
        ]
        
        missing_vars = [var_name for var_name, var_value in required_vars if not var_value]
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
        
        return True
