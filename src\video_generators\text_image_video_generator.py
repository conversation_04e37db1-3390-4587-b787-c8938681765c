"""
Text + Image video generator for creating Instagram reels from text messages with images
"""
import os
import random
from pathlib import Path
from typing import Optional, List, Tuple

from PIL import Image, ImageDraw, ImageFont, ImageFilter
import cv2
import numpy as np
from moviepy.editor import VideoFileClip, AudioFileClip, CompositeVideoClip, ImageClip
import arabic_reshaper
from bidi.algorithm import get_display

from src.config import Config
from src.utils.logger import logger
from src.video_generators.text_video_generator import TextVideoGenerator

class TextImageVideoGenerator(TextVideoGenerator):
    """Generates Instagram reel videos from text content with images"""
    
    def __init__(self):
        super().__init__()
        self.image_transition_duration = 2.0  # Duration for each image
    
    def _resize_image_to_fit(self, image: Image.Image, max_width: int, max_height: int) -> Image.Image:
        """Resize image to fit within specified dimensions while maintaining aspect ratio"""
        original_width, original_height = image.size
        
        # Calculate scaling factor
        width_ratio = max_width / original_width
        height_ratio = max_height / original_height
        scale_factor = min(width_ratio, height_ratio)
        
        # Calculate new dimensions
        new_width = int(original_width * scale_factor)
        new_height = int(original_height * scale_factor)
        
        # Resize image
        resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        return resized_image
    
    def _create_image_collage(self, images: List[Image.Image], canvas_width: int, canvas_height: int) -> Image.Image:
        """Create a collage from multiple images"""
        if not images:
            return None
        
        if len(images) == 1:
            # Single image - center it
            img = self._resize_image_to_fit(images[0], canvas_width, canvas_height)
            canvas = Image.new('RGB', (canvas_width, canvas_height), (40, 40, 40))
            x = (canvas_width - img.width) // 2
            y = (canvas_height - img.height) // 2
            canvas.paste(img, (x, y))
            return canvas
        
        elif len(images) == 2:
            # Two images - side by side
            img_width = canvas_width // 2 - 10
            img_height = canvas_height - 20
            
            canvas = Image.new('RGB', (canvas_width, canvas_height), (40, 40, 40))
            
            img1 = self._resize_image_to_fit(images[0], img_width, img_height)
            img2 = self._resize_image_to_fit(images[1], img_width, img_height)
            
            x1 = 10
            y1 = (canvas_height - img1.height) // 2
            canvas.paste(img1, (x1, y1))
            
            x2 = canvas_width // 2 + 5
            y2 = (canvas_height - img2.height) // 2
            canvas.paste(img2, (x2, y2))
            
            return canvas
        
        else:
            # Multiple images - grid layout
            cols = 2 if len(images) <= 4 else 3
            rows = (len(images) + cols - 1) // cols
            
            img_width = (canvas_width - (cols + 1) * 10) // cols
            img_height = (canvas_height - (rows + 1) * 10) // rows
            
            canvas = Image.new('RGB', (canvas_width, canvas_height), (40, 40, 40))
            
            for i, img in enumerate(images[:9]):  # Max 9 images
                row = i // cols
                col = i % cols
                
                resized_img = self._resize_image_to_fit(img, img_width, img_height)
                
                x = 10 + col * (img_width + 10) + (img_width - resized_img.width) // 2
                y = 10 + row * (img_height + 10) + (img_height - resized_img.height) // 2
                
                canvas.paste(resized_img, (x, y))
            
            return canvas
    
    def _create_text_image_frame(self, text: str, images: List[Image.Image]) -> Image.Image:
        """Create a frame combining text and images with improved layout"""
        # Create base image with gradient background
        img = Image.new('RGB', (self.width, self.height), self.background_color)

        # Create gradient background
        for y in range(self.height):
            color_factor = y / self.height
            r = int(self.background_color[0] * (1 - color_factor * 0.3))
            g = int(self.background_color[1] * (1 - color_factor * 0.3))
            b = int(self.background_color[2] * (1 - color_factor * 0.3))
            ImageDraw.Draw(img).line([(0, y), (self.width, y)], fill=(r, g, b))

        draw = ImageDraw.Draw(img)

        # Calculate layout areas with better proportions
        margin = 50
        content_width = self.width - (2 * margin)

        # Better balanced layout: 35% text, 55% images, 10% branding/spacing
        text_height = int(self.height * 0.35)
        text_area_y = margin + 20

        # Image area positioned closer to text
        image_height = int(self.height * 0.55)
        image_area_y = text_area_y + text_height + 30  # Reduced gap
        
        # Calculate responsive font size and wrap text with better proportions
        text_area_width = content_width - 100  # Better margins
        text_area_height = text_height - 60  # More space for text

        # Calculate optimal font size and layout
        optimal_font_size, required_text_height, lines = self._calculate_optimal_font_and_layout(
            text, text_area_width, text_area_height
        )

        # Create responsive font
        responsive_font = ImageFont.truetype(str(self.font_path), optimal_font_size)

        # If we didn't get lines from the calculation, wrap them now
        if not lines:
            lines = self._wrap_text_with_font(text, text_area_width, responsive_font)

        # Recalculate text area height based on actual needs
        line_height = int(optimal_font_size * 1.4)
        actual_text_height = len(lines) * line_height

        # Expand text area height if needed
        if actual_text_height > text_area_height:
            text_area_height = min(actual_text_height + 40, text_height - 20)  # Expand but leave some margin
        
        # Create text background with responsive sizing - expand as needed
        line_height = int(optimal_font_size * 1.4)
        text_bg_height = len(lines) * line_height + 80  # More padding for better appearance

        # Center the text background, but allow it to expand beyond original text area if needed
        ideal_text_bg_y = text_area_y + (text_area_height - text_bg_height) // 2

        # Ensure background fits within the overall frame, but prioritize text readability
        min_y = text_area_y - 20  # Allow some expansion above
        max_y = text_area_y + text_height + 20  # Allow some expansion below

        text_bg_y = max(min_y, min(ideal_text_bg_y, max_y - text_bg_height))

        # If text is too tall, expand the background height and adjust position
        if text_bg_height > text_area_height + 40:
            text_bg_y = max(min_y, text_area_y - 10)
            # Ensure it doesn't go below the frame
            if text_bg_y + text_bg_height > max_y:
                text_bg_height = max_y - text_bg_y

        # Calculate text background width and position
        text_bg_padding = 50
        text_bg_width = min(text_area_width + (2 * text_bg_padding), content_width)
        text_bg_x = (self.width - text_bg_width) // 2

        # Create gradient background for text container
        text_bg = Image.new('RGBA', (text_bg_width, text_bg_height), (0, 0, 0, 0))
        text_bg_draw = ImageDraw.Draw(text_bg)

        for y in range(text_bg_height):
            factor = y / text_bg_height
            r = int(45 + factor * 15)  # Gradient from dark to slightly lighter
            g = int(55 + factor * 15)
            b = int(70 + factor * 15)
            text_bg_draw.line([(0, y), (text_bg_width, y)], fill=(r, g, b, 240))

        # Create rounded rectangle mask
        text_mask = Image.new('L', (text_bg_width, text_bg_height), 0)
        text_mask_draw = ImageDraw.Draw(text_mask)
        text_mask_draw.rounded_rectangle([0, 0, text_bg_width, text_bg_height], radius=25, fill=255)
        text_bg.putalpha(text_mask)

        # Add shadow effect
        from PIL import ImageFilter
        shadow_img = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
        shadow_draw = ImageDraw.Draw(shadow_img)

        # Create layered shadow
        for i in range(3):
            offset = 8 - (i * 2)
            alpha = 60 - (i * 15)
            shadow_draw.rounded_rectangle(
                [text_bg_x + offset, text_bg_y + offset,
                 text_bg_x + text_bg_width + offset, text_bg_y + text_bg_height + offset],
                radius=25,
                fill=(0, 0, 0, alpha)
            )

        shadow_img = shadow_img.filter(ImageFilter.GaussianBlur(radius=4))
        img = img.convert('RGBA')
        img = Image.alpha_composite(img, shadow_img)
        img.paste(text_bg, (text_bg_x, text_bg_y), text_bg)
        img = img.convert('RGB')

        # Add subtle border
        draw = ImageDraw.Draw(img)
        draw.rounded_rectangle(
            [text_bg_x, text_bg_y, text_bg_x + text_bg_width, text_bg_y + text_bg_height],
            radius=25,
            outline=(80, 90, 105),
            width=2
        )

        # Draw text with responsive font, emoji support, and shadows
        start_y = text_bg_y + text_bg_padding

        for i, line in enumerate(lines):
            y_pos = start_y + (i * line_height)
            # Use emoji-aware width calculation
            text_width = self._calculate_text_width_with_emojis(line, responsive_font)
            x_pos = (self.width - text_width) // 2

            # Ensure text stays within bounds
            max_x = text_bg_x + text_bg_width - 20
            if x_pos + text_width > max_x:
                x_pos = max_x - text_width
            if x_pos < text_bg_x + 20:
                x_pos = text_bg_x + 20

            # Draw text shadow for better readability
            shadow_offset = 2
            try:
                self._draw_text_with_emoji(draw, (x_pos + shadow_offset, y_pos + shadow_offset), line, responsive_font, (0, 0, 0, 100))
            except:
                draw.text((x_pos + shadow_offset, y_pos + shadow_offset), line, font=responsive_font, fill=(0, 0, 0, 100))

            # Draw main text with emoji support
            self._draw_text_with_emoji(draw, (x_pos, y_pos), line, responsive_font, self.text_color)
        
        # Create and add image collage with enhanced styling
        if images:
            collage = self._create_image_collage(images, content_width - 40, image_height - 60)
            if collage:
                # Position collage closer to text
                collage_x = (self.width - collage.width) // 2
                collage_y = image_area_y + 10  # Reduced gap from text

                # Create shadow for collage
                shadow_img = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
                shadow_draw = ImageDraw.Draw(shadow_img)

                # Create layered shadow for collage
                for i in range(3):
                    offset = 10 - (i * 3)
                    alpha = 50 - (i * 12)
                    shadow_draw.rounded_rectangle(
                        [collage_x + offset, collage_y + offset,
                         collage_x + collage.width + offset, collage_y + collage.height + offset],
                        radius=25,
                        fill=(0, 0, 0, alpha)
                    )

                shadow_img = shadow_img.filter(ImageFilter.GaussianBlur(radius=5))
                img = img.convert('RGBA')
                img = Image.alpha_composite(img, shadow_img)

                # Add rounded corners to collage
                mask = Image.new('L', collage.size, 0)
                mask_draw = ImageDraw.Draw(mask)
                mask_draw.rounded_rectangle([0, 0, collage.width, collage.height], radius=25, fill=255)

                # Apply mask
                collage.putalpha(mask)

                # Paste collage onto main image
                img.paste(collage, (collage_x, collage_y), collage)
                img = img.convert('RGB')

                # Add subtle border to collage
                draw = ImageDraw.Draw(img)
                draw.rounded_rectangle(
                    [collage_x, collage_y, collage_x + collage.width, collage_y + collage.height],
                    radius=25,
                    outline=(80, 90, 105),
                    width=2
                )
        
        # Add logo with enhanced styling
        logo_path = self._get_brand_logo("small")
        if logo_path:
            try:
                logo = Image.open(logo_path).resize((70, 70), Image.Resampling.LANCZOS)
                logo_x = self.width - 90
                logo_y = 30

                # Add subtle shadow for logo
                if logo.mode == 'RGBA':
                    # Create shadow
                    shadow = Image.new('RGBA', (logo.width + 4, logo.height + 4), (0, 0, 0, 0))
                    shadow_draw = ImageDraw.Draw(shadow)
                    shadow_draw.ellipse([2, 2, logo.width + 2, logo.height + 2], fill=(0, 0, 0, 50))

                    img_rgba = img.convert('RGBA')
                    img_rgba.paste(shadow, (logo_x - 2, logo_y + 2), shadow)
                    img_rgba.paste(logo, (logo_x, logo_y), logo)
                    img = img_rgba.convert('RGB')
                else:
                    img.paste(logo, (logo_x, logo_y))
            except Exception as e:
                logger.warning(f"Could not add logo: {e}")

        # Add channel branding with enhanced styling
        brand_text = f"@{Config.CHANNEL_NAME}"
        brand_bbox = self.small_font.getbbox(brand_text)
        brand_width = brand_bbox[2] - brand_bbox[0]
        brand_x = self.width - brand_width - 40
        brand_y = self.height - 60

        # Add gradient background for brand text
        brand_bg = Image.new('RGBA', (brand_width + 30, 35), (0, 0, 0, 0))
        brand_bg_draw = ImageDraw.Draw(brand_bg)

        # Create gradient background
        for y in range(35):
            factor = y / 35
            r = int(self.accent_color[0] * (1 - factor * 0.2))
            g = int(self.accent_color[1] * (1 - factor * 0.2))
            b = int(self.accent_color[2] * (1 - factor * 0.2))
            brand_bg_draw.line([(0, y), (brand_width + 30, y)], fill=(r, g, b, 200))

        # Create rounded rectangle mask for brand background
        brand_mask = Image.new('L', (brand_width + 30, 35), 0)
        brand_mask_draw = ImageDraw.Draw(brand_mask)
        brand_mask_draw.rounded_rectangle([0, 0, brand_width + 30, 35], radius=15, fill=255)
        brand_bg.putalpha(brand_mask)

        # Paste brand background
        img_rgba = img.convert('RGBA')
        img_rgba.paste(brand_bg, (brand_x - 15, brand_y - 5), brand_bg)
        img = img_rgba.convert('RGB')

        # Draw brand text with shadow
        draw = ImageDraw.Draw(img)
        draw.text((brand_x + 1, brand_y + 1), brand_text, font=self.small_font, fill=(0, 0, 0, 100))  # Shadow
        draw.text((brand_x, brand_y), brand_text, font=self.small_font, fill=(255, 255, 255))

        return img
    
    def _load_images_from_paths(self, image_paths: List[Path]) -> List[Image.Image]:
        """Load images from file paths"""
        images = []
        
        for path in image_paths:
            try:
                if path.exists() and path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.webp']:
                    img = Image.open(path)
                    # Convert to RGB if necessary
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    images.append(img)
                    logger.info(f"Loaded image: {path}")
                else:
                    logger.warning(f"Skipping invalid image: {path}")
            except Exception as e:
                logger.error(f"Error loading image {path}: {e}")
        
        return images
    
    def generate_video(self, text: str, image_paths: List[Path], output_path: Path) -> bool:
        """
        Generate a video from text content and images
        
        Args:
            text: The text content
            image_paths: List of paths to image files
            output_path: Path where the video should be saved
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Generating text+image video: {output_path}")
            
            # Load images
            images = self._load_images_from_paths(image_paths)
            
            if not images:
                logger.warning("No valid images found, falling back to text-only video")
                return super().generate_video(text, output_path)
            
            # Create frames for different image combinations if multiple images
            frames = []
            
            if len(images) == 1:
                # Single image - one frame
                frame = self._create_text_image_frame(text, images)
                frames.append(frame)
            
            else:
                # Multiple images - create transitions
                # First frame: all images
                frame_all = self._create_text_image_frame(text, images)
                frames.append(frame_all)
                
                # Individual image frames
                for img in images[:3]:  # Max 3 individual frames
                    frame_single = self._create_text_image_frame(text, [img])
                    frames.append(frame_single)
            
            # Save frames as temporary images
            temp_image_paths = []
            for i, frame in enumerate(frames):
                temp_path = Config.TEMP_DIR / f"temp_frame_{random.randint(1000, 9999)}_{i}.png"
                frame.save(temp_path)
                temp_image_paths.append(temp_path)
            
            # Create video clips from frames
            clips = []
            frame_duration = self.duration / len(frames)
            
            for temp_path in temp_image_paths:
                clip = ImageClip(str(temp_path)).set_duration(frame_duration)
                clips.append(clip)
            
            # Concatenate clips
            if len(clips) > 1:
                video_clip = CompositeVideoClip(clips, method='compose')
            else:
                video_clip = clips[0]
            
            # Add background music
            background_music = self._get_random_background_music()
            if background_music:
                try:
                    audio_clip = AudioFileClip(str(background_music))
                    if audio_clip.duration < self.duration:
                        audio_clip = audio_clip.loop(duration=self.duration)
                    else:
                        audio_clip = audio_clip.subclip(0, self.duration)
                    
                    audio_clip = audio_clip.volumex(0.3)
                    video_clip = video_clip.set_audio(audio_clip)
                    
                except Exception as e:
                    logger.warning(f"Could not add background music: {e}")
            
            # Write video file with optimized settings
            video_clip.write_videofile(
                str(output_path),
                fps=self.fps,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile=str(Config.TEMP_DIR / 'temp_audio.m4a'),
                remove_temp=True,
                verbose=False,
                logger=None,
                # Optimization settings for faster encoding
                preset='ultrafast',  # Fastest encoding preset
                ffmpeg_params=['-crf', '23', '-threads', '4']  # Good quality, multi-threading
            )
            
            # Clean up
            video_clip.close()
            if background_music and 'audio_clip' in locals():
                audio_clip.close()
            
            # Remove temporary images
            for temp_path in temp_image_paths:
                if temp_path.exists():
                    temp_path.unlink()
            
            logger.info(f"Successfully generated text+image video: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error generating text+image video: {e}")
            return False

    def _calculate_optimal_font_and_layout(self, text: str, max_width: int, initial_max_height: int) -> tuple:
        """Calculate optimal font size and required height for text layout"""
        # Use a good readable font size - don't go too small
        preferred_font_size = min(self.font_size, 36)  # Good readable size for text+image
        min_font_size = 22  # Minimum readable size

        # Try preferred font size first
        for test_font_size in [preferred_font_size, preferred_font_size - 4, preferred_font_size - 8, min_font_size]:
            try:
                test_font = ImageFont.truetype(str(self.font_path), test_font_size)

                # Test wrap with this font size
                lines = self._wrap_text_with_font(text, max_width, test_font)

                # Check if individual lines fit width using emoji-aware calculation
                fits_width = True
                for line in lines:
                    try:
                        line_width = self._calculate_text_width_with_emojis(line, test_font)
                        if line_width > max_width:
                            fits_width = False
                            break
                    except:
                        fits_width = False
                        break

                if fits_width and len(lines) <= 10:  # Allow up to 10 lines for text+image
                    # Calculate required height
                    line_height = int(test_font_size * 1.4)
                    required_height = len(lines) * line_height + 40  # Extra padding

                    return test_font_size, required_height, lines

            except Exception:
                continue

        # Fallback
        return min_font_size, initial_max_height, []
