"""
Video + Text generator for creating Instagram reels from existing videos with text overlay
"""
import os
import random
from pathlib import Path
from typing import Optional, List, Tuple

from PIL import Image, ImageDraw, ImageFont
import cv2
import numpy as np
from moviepy.editor import VideoFileClip, AudioFileClip, CompositeVideoClip, ImageClip, concatenate_videoclips
import arabic_reshaper
from bidi.algorithm import get_display

from src.config import Config
from src.utils.logger import logger
from src.video_generators.text_video_generator import TextVideoGenerator

class VideoTextGenerator(TextVideoGenerator):
    """Generates Instagram reel videos from existing videos with text overlay"""
    
    def __init__(self):
        super().__init__()
        self.target_aspect_ratio = self.height / self.width  # 16:9 -> 9:16 for Instagram reels
    
    def _resize_video_to_reel_format(self, video_clip: VideoFileClip) -> VideoFileClip:
        """Resize video to Instagram reel format (9:16 aspect ratio)"""
        original_width, original_height = video_clip.size
        original_aspect_ratio = original_height / original_width
        
        if abs(original_aspect_ratio - self.target_aspect_ratio) < 0.1:
            # Already close to target aspect ratio, just resize
            return video_clip.resize((self.width, self.height))
        
        # Calculate new dimensions to fit within reel format
        if original_aspect_ratio > self.target_aspect_ratio:
            # Video is taller than target, fit by width
            new_width = self.width
            new_height = int(original_height * (self.width / original_width))
            
            # Crop height if necessary
            if new_height > self.height:
                resized_clip = video_clip.resize((new_width, new_height))
                # Center crop
                y_center = new_height // 2
                crop_height = self.height
                y_start = max(0, y_center - crop_height // 2)
                y_end = min(new_height, y_start + crop_height)
                return resized_clip.crop(y1=y_start, y2=y_end)
            else:
                # Add padding if needed
                resized_clip = video_clip.resize((new_width, new_height))
                return resized_clip.on_color(size=(self.width, self.height), color=(0, 0, 0))
        
        else:
            # Video is wider than target, fit by height
            new_height = self.height
            new_width = int(original_width * (self.height / original_height))
            
            # Crop width if necessary
            if new_width > self.width:
                resized_clip = video_clip.resize((new_width, new_height))
                # Center crop
                x_center = new_width // 2
                crop_width = self.width
                x_start = max(0, x_center - crop_width // 2)
                x_end = min(new_width, x_start + crop_width)
                return resized_clip.crop(x1=x_start, x2=x_end)
            else:
                # Add padding if needed
                resized_clip = video_clip.resize((new_width, new_height))
                return resized_clip.on_color(size=(self.width, self.height), color=(0, 0, 0))
    
    def _create_text_overlay_image(self, text: str, video_width: int, video_height: int) -> Image.Image:
        """Create a text overlay image using PIL (no ImageMagick required)"""
        try:
            # Prepare Farsi text
            display_text = self._prepare_farsi_text(text)

            # Create transparent overlay image
            overlay = Image.new('RGBA', (video_width, video_height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(overlay)

            # Text area settings
            text_area_width = video_width - 100  # Leave margins
            text_area_height = 200  # Max height for overlay

            # Calculate optimal font size and layout for overlay
            optimal_font_size, required_height, lines = self._calculate_optimal_font_and_layout(
                display_text, text_area_width, text_area_height
            )

            # Create responsive font
            try:
                responsive_font = ImageFont.truetype(str(self.font_path), optimal_font_size)

                # If we didn't get lines from the calculation, wrap them now
                if not lines:
                    lines = self._wrap_text_with_font(display_text, text_area_width, responsive_font)
            except:
                # Fallback font and simple wrapping
                responsive_font = ImageFont.load_default()
                words = display_text.split()
                lines = []
                current_line = []
                max_chars_per_line = 30

                for word in words:
                    if len(' '.join(current_line + [word])) <= max_chars_per_line:
                        current_line.append(word)
                    else:
                        if current_line:
                            lines.append(' '.join(current_line))
                            current_line = [word]
                        else:
                            lines.append(word)

                if current_line:
                    lines.append(' '.join(current_line))

            # Limit to 4 lines for overlay
            if len(lines) > 4:
                lines = lines[:4]
                if len(lines[3]) > 40:
                    lines[3] = lines[3][:37] + "..."

            # Calculate text positioning (bottom of video with margin)
            line_height = int(optimal_font_size * 1.4)
            total_text_height = len(lines) * line_height
            start_y = video_height - total_text_height - 100  # 100px margin from bottom

            # Draw each line with stroke effect
            for i, line in enumerate(lines):
                if not line.strip():
                    continue

                # Calculate line position (centered horizontally) with emoji-aware width
                try:
                    line_width = self._calculate_text_width_with_emojis(line, responsive_font)
                except:
                    line_width = len(line) * (optimal_font_size * 0.6)

                x = (video_width - line_width) // 2
                y = start_y + (i * line_height)

                # Draw stroke (black outline)
                stroke_width = 3
                for dx in range(-stroke_width, stroke_width + 1):
                    for dy in range(-stroke_width, stroke_width + 1):
                        if dx*dx + dy*dy <= stroke_width*stroke_width:
                            try:
                                self._draw_text_with_emoji(draw, (x + dx, y + dy), line, responsive_font, 'black')
                            except:
                                draw.text((x + dx, y + dy), line, font=responsive_font, fill='black')

                # Draw main text (white)
                try:
                    self._draw_text_with_emoji(draw, (x, y), line, responsive_font, 'white')
                except:
                    draw.text((x, y), line, font=responsive_font, fill='white')

            return overlay

        except Exception as e:
            logger.error(f"Error creating text overlay image: {e}")
            # Return empty transparent image
            return Image.new('RGBA', (video_width, video_height), (0, 0, 0, 0))
    
    def _add_branding_to_overlay(self, overlay_image: Image.Image) -> Image.Image:
        """Add channel branding to overlay image"""
        try:
            draw = ImageDraw.Draw(overlay_image)
            brand_text = f"@{Config.INSTAGRAM_HANDLE}"

            # Create branding font
            brand_font_size = int(self.font_size * 0.5)
            try:
                brand_font = ImageFont.truetype(str(self.font_path), brand_font_size)
            except:
                brand_font = ImageFont.load_default()

            # Calculate position (top right)
            try:
                bbox = brand_font.getbbox(brand_text)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
            except:
                text_width = len(brand_text) * (brand_font_size * 0.6)
                text_height = brand_font_size

            x = overlay_image.width - text_width - 20
            y = 20

            # Draw stroke (black outline)
            stroke_width = 1
            for dx in range(-stroke_width, stroke_width + 1):
                for dy in range(-stroke_width, stroke_width + 1):
                    if dx*dx + dy*dy <= stroke_width*stroke_width:
                        draw.text((x + dx, y + dy), brand_text, font=brand_font, fill='black')

            # Draw main text (accent color)
            draw.text((x, y), brand_text, font=brand_font, fill=self.accent_color)

            return overlay_image

        except Exception as e:
            logger.error(f"Error adding branding overlay: {e}")
            return overlay_image
    
    def _trim_video_to_duration(self, video_clip: VideoFileClip, target_duration: float) -> VideoFileClip:
        """Trim video to target duration"""
        if video_clip.duration <= target_duration:
            return video_clip
        
        # Take from the middle of the video for best content
        start_time = (video_clip.duration - target_duration) / 2
        end_time = start_time + target_duration
        
        return video_clip.subclip(start_time, end_time)
    
    def generate_video(self, text: str, video_path: Path, output_path: Path) -> bool:
        """
        Generate an Instagram reel from existing video with text overlay

        Args:
            text: The text content to overlay
            video_path: Path to the source video file
            output_path: Path where the processed video should be saved

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Generating video+text reel: {output_path}")

            if not video_path.exists():
                logger.error(f"Source video not found: {video_path}")
                return False

            # Load source video
            source_video = VideoFileClip(str(video_path))
            logger.info(f"Loaded source video: {source_video.duration:.2f}s, {source_video.size}")

            # Keep original duration - don't trim unless it's extremely long
            original_duration = source_video.duration
            if original_duration > 60:  # Only trim if longer than 60 seconds
                source_video = self._trim_video_to_duration(source_video, 60)
                logger.info(f"Trimmed very long video from {original_duration:.2f}s to 60s")
            else:
                logger.info(f"Keeping original duration: {original_duration:.2f}s")

            # Resize to Instagram reel format
            formatted_video = self._resize_video_to_reel_format(source_video)
            logger.info(f"Resized video to reel format: {formatted_video.size}")

            # Create text overlay image using PIL (no ImageMagick required)
            if text.strip():
                overlay_image = self._create_text_overlay_image(text, formatted_video.w, formatted_video.h)

                # Add branding to the same overlay
                overlay_image = self._add_branding_to_overlay(overlay_image)

                # Convert PIL image to MoviePy ImageClip
                import numpy as np
                overlay_array = np.array(overlay_image)
                overlay_clip = ImageClip(overlay_array, duration=formatted_video.duration, ismask=False)
                overlay_clip = overlay_clip.set_opacity(0.9)  # Slight transparency

                # Compose final video with overlay
                final_video = CompositeVideoClip([formatted_video, overlay_clip])
                logger.info("Added text and branding overlay")
            else:
                # No text, just add branding
                overlay_image = Image.new('RGBA', (formatted_video.w, formatted_video.h), (0, 0, 0, 0))
                overlay_image = self._add_branding_to_overlay(overlay_image)

                import numpy as np
                overlay_array = np.array(overlay_image)
                overlay_clip = ImageClip(overlay_array, duration=formatted_video.duration, ismask=False)

                final_video = CompositeVideoClip([formatted_video, overlay_clip])
                logger.info("Added branding overlay only")

            # IMPORTANT: Preserve original audio - don't add background music
            if source_video.audio:
                final_video = final_video.set_audio(source_video.audio)
                logger.info("Preserved original video audio (no background music added)")
            else:
                logger.info("Source video has no audio")

            # Write final video with optimized settings for faster processing
            final_video.write_videofile(
                str(output_path),
                fps=self.fps,
                codec='libx264',
                audio_codec='aac' if source_video.audio else None,
                temp_audiofile=str(Config.TEMP_DIR / 'temp_audio.m4a') if source_video.audio else None,
                remove_temp=True,
                verbose=False,
                logger=None,
                # Optimization settings for faster encoding
                preset='ultrafast',  # Fastest encoding preset
                ffmpeg_params=['-crf', '23', '-threads', '4']  # Good quality, multi-threading
            )

            # Clean up
            source_video.close()
            formatted_video.close()
            final_video.close()

            logger.info(f"Successfully generated video+text reel: {output_path}")
            logger.info(f"Final video duration: {original_duration:.2f}s (preserved from original)")
            return True

        except Exception as e:
            logger.error(f"Error generating video+text reel: {e}")
            return False
    
    def extract_video_thumbnail(self, video_path: Path, output_path: Path, time_offset: float = None) -> bool:
        """
        Extract a thumbnail from the video
        
        Args:
            video_path: Path to the source video
            output_path: Path where thumbnail should be saved
            time_offset: Time offset to extract thumbnail (default: middle of video)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            video_clip = VideoFileClip(str(video_path))
            
            if time_offset is None:
                time_offset = video_clip.duration / 2
            
            # Extract frame
            frame = video_clip.get_frame(time_offset)
            
            # Convert to PIL Image and save
            thumbnail = Image.fromarray(frame)
            thumbnail.save(output_path)
            
            video_clip.close()
            
            logger.info(f"Extracted thumbnail: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error extracting thumbnail: {e}")
            return False

    def _calculate_optimal_font_and_layout(self, text: str, max_width: int, initial_max_height: int) -> tuple:
        """Calculate optimal font size and required height for text layout"""
        # Use a good readable font size for video overlay
        preferred_font_size = min(self.font_size, 32)  # Good readable size for video overlay
        min_font_size = 20  # Minimum readable size for video

        # Try preferred font size first
        for test_font_size in [preferred_font_size, preferred_font_size - 3, preferred_font_size - 6, min_font_size]:
            try:
                test_font = ImageFont.truetype(str(self.font_path), test_font_size)

                # Test wrap with this font size
                lines = self._wrap_text_with_font(text, max_width, test_font)

                # Check if individual lines fit width using emoji-aware calculation
                fits_width = True
                for line in lines:
                    try:
                        line_width = self._calculate_text_width_with_emojis(line, test_font)
                        if line_width > max_width:
                            fits_width = False
                            break
                    except:
                        fits_width = False
                        break

                if fits_width and len(lines) <= 6:  # Allow up to 6 lines for video overlay
                    # Calculate required height
                    line_height = int(test_font_size * 1.4)
                    required_height = len(lines) * line_height + 30  # Extra padding

                    return test_font_size, required_height, lines

            except Exception:
                continue

        # Fallback
        return min_font_size, initial_max_height, []
