"""
Scheduling system for posting Instagram reels at optimal times
"""
import json
import random
import schedule
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional
import threading

from src.config import Config
from src.utils.logger import logger
from src.message_processor import MessageProcessor, ProcessedMessage
from src.instagram_client import InstagramClient

class PostScheduler:
    """Handles scheduling and posting of Instagram reels"""
    
    def __init__(self):
        self.message_processor = MessageProcessor()
        self.instagram_client = InstagramClient()
        self.schedule_file = Config.BASE_DIR / "post_schedule.json"
        self.scheduled_posts = self._load_schedule()
        self.is_running = False
        self.scheduler_thread = None
    
    def _load_schedule(self) -> List[Dict[str, Any]]:
        """Load scheduled posts from file"""
        if self.schedule_file.exists():
            try:
                with open(self.schedule_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('scheduled_posts', [])
            except Exception as e:
                logger.warning(f"Could not load schedule: {e}")
        return []
    
    def _save_schedule(self):
        """Save scheduled posts to file"""
        try:
            data = {
                'scheduled_posts': self.scheduled_posts,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.schedule_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Could not save schedule: {e}")
    
    def _get_optimal_posting_times(self) -> List[int]:
        """
        Get optimal posting times (hours) for Instagram engagement
        Based on general Instagram engagement patterns
        """
        # Peak engagement times (in 24-hour format)
        weekday_peaks = [8, 11, 13, 17, 19, 21]  # Morning, lunch, evening
        weekend_peaks = [9, 12, 15, 18, 20]      # More relaxed schedule
        
        now = datetime.now()
        if now.weekday() < 5:  # Monday = 0, Sunday = 6
            return weekday_peaks
        else:
            return weekend_peaks
    
    def _generate_random_post_time(self) -> datetime:
        """Generate a random posting time within optimal hours"""
        now = datetime.now()
        
        # Get optimal hours for today or tomorrow
        optimal_hours = self._get_optimal_posting_times()
        
        # Choose a random optimal hour
        target_hour = random.choice(optimal_hours)
        
        # Add some randomness to minutes (0-59)
        target_minute = random.randint(0, 59)
        
        # Create target datetime
        target_time = now.replace(hour=target_hour, minute=target_minute, second=0, microsecond=0)
        
        # If the time has already passed today, schedule for tomorrow
        if target_time <= now:
            target_time += timedelta(days=1)
            # Recalculate optimal hours for tomorrow
            tomorrow = target_time.date()
            if tomorrow.weekday() < 5:
                optimal_hours = [8, 11, 13, 17, 19, 21]
            else:
                optimal_hours = [9, 12, 15, 18, 20]
            target_hour = random.choice(optimal_hours)
            target_time = target_time.replace(hour=target_hour)
        
        # Add random interval between posts (2-8 hours as configured)
        min_interval = Config.MIN_POST_INTERVAL_HOURS
        max_interval = Config.MAX_POST_INTERVAL_HOURS
        additional_hours = random.uniform(min_interval, max_interval)
        
        target_time += timedelta(hours=additional_hours)
        
        return target_time
    
    def schedule_post(self, processed_message: ProcessedMessage) -> bool:
        """
        Schedule a processed message for posting
        
        Args:
            processed_message: The message to schedule
            
        Returns:
            True if scheduled successfully, False otherwise
        """
        try:
            if not processed_message.video_generated or not processed_message.output_video_path:
                logger.error("Cannot schedule post: video not generated")
                return False
            
            # Generate random posting time
            post_time = self._generate_random_post_time()
            
            # Create schedule entry
            schedule_entry = {
                'id': f"post_{processed_message.telegram_message.message_id}_{int(time.time())}",
                'message_id': processed_message.telegram_message.message_id,
                'video_path': str(processed_message.output_video_path),
                'caption': processed_message.text_content,
                'scheduled_time': post_time.isoformat(),
                'status': 'scheduled',
                'created_at': datetime.now().isoformat(),
                'attempts': 0,
                'max_attempts': 3
            }
            
            self.scheduled_posts.append(schedule_entry)
            self._save_schedule()
            
            logger.info(f"Scheduled post for {post_time.strftime('%Y-%m-%d %H:%M:%S')}: {processed_message.telegram_message.message_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error scheduling post: {e}")
            return False
    
    def schedule_ready_posts(self) -> int:
        """
        Schedule all posts that are ready for posting
        
        Returns:
            Number of posts scheduled
        """
        try:
            ready_messages = self.message_processor.get_ready_for_posting()
            scheduled_count = 0
            
            for message in ready_messages:
                if self.schedule_post(message):
                    scheduled_count += 1
            
            logger.info(f"Scheduled {scheduled_count} posts")
            return scheduled_count
            
        except Exception as e:
            logger.error(f"Error scheduling ready posts: {e}")
            return 0
    
    def _execute_scheduled_post(self, schedule_entry: Dict[str, Any]) -> bool:
        """Execute a scheduled post"""
        try:
            video_path = Path(schedule_entry['video_path'])
            caption = schedule_entry['caption']
            
            logger.info(f"Executing scheduled post: {schedule_entry['id']}")

            success = False
            media_id = None

            # Post to Instagram (if enabled)
            if Config.ENABLE_INSTAGRAM_POSTING:
                media_id = self.instagram_client.post_reel(video_path, caption)
                if media_id:
                    logger.info(f"Successfully posted to Instagram: {media_id}")
                    success = True
                else:
                    logger.warning("Instagram posting failed")
            else:
                logger.info("Instagram posting disabled, skipping")
                success = True  # Consider successful if Instagram is disabled

            # Send video to Telegram (if enabled)
            if Config.ENABLE_TELEGRAM_VIDEO_SENDING:
                try:
                    import asyncio
                    from .telegram_client import TelegramClient

                    # Create Telegram client for sending
                    telegram_client = TelegramClient()

                    # Create caption for Telegram
                    telegram_caption = f"📹 <b>New Video Generated</b>\n\n{caption}\n\n🔗 @linkychannell"

                    # Send video
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    telegram_success = loop.run_until_complete(
                        telegram_client.send_video(video_path, telegram_caption)
                    )
                    loop.close()

                    if telegram_success:
                        logger.info("Successfully sent video to Telegram")
                    else:
                        logger.warning("Failed to send video to Telegram")

                except Exception as e:
                    logger.error(f"Error sending video to Telegram: {e}")

            if success:
                # Update schedule entry
                schedule_entry['status'] = 'posted'
                schedule_entry['posted_at'] = datetime.now().isoformat()
                if media_id:
                    schedule_entry['media_id'] = media_id

                # Mark message as posted
                message_id = schedule_entry['message_id']
                for message in self.message_processor.processing_queue:
                    if message.telegram_message.message_id == message_id:
                        self.message_processor.mark_instagram_posted(message)
                        break

                logger.info(f"Successfully executed scheduled post: {schedule_entry['id']}")
                return True
            else:
                # Update attempt count
                schedule_entry['attempts'] += 1
                schedule_entry['last_attempt'] = datetime.now().isoformat()
                
                if schedule_entry['attempts'] >= schedule_entry['max_attempts']:
                    schedule_entry['status'] = 'failed'
                    logger.error(f"Post failed after {schedule_entry['max_attempts']} attempts: {schedule_entry['id']}")
                else:
                    # Reschedule for later
                    new_time = datetime.now() + timedelta(hours=2)  # Try again in 2 hours
                    schedule_entry['scheduled_time'] = new_time.isoformat()
                    logger.warning(f"Post failed, rescheduled for {new_time}: {schedule_entry['id']}")
                
                return False
                
        except Exception as e:
            logger.error(f"Error executing scheduled post: {e}")
            schedule_entry['attempts'] += 1
            schedule_entry['last_error'] = str(e)
            schedule_entry['last_attempt'] = datetime.now().isoformat()
            
            if schedule_entry['attempts'] >= schedule_entry['max_attempts']:
                schedule_entry['status'] = 'failed'
            
            return False
    
    def check_and_execute_posts(self):
        """Check for posts that should be executed now"""
        try:
            now = datetime.now()
            executed_count = 0
            
            for schedule_entry in self.scheduled_posts:
                if schedule_entry['status'] != 'scheduled':
                    continue
                
                scheduled_time = datetime.fromisoformat(schedule_entry['scheduled_time'])
                
                if now >= scheduled_time:
                    if self._execute_scheduled_post(schedule_entry):
                        executed_count += 1
            
            if executed_count > 0:
                self._save_schedule()
                logger.info(f"Executed {executed_count} scheduled posts")
            
        except Exception as e:
            logger.error(f"Error checking and executing posts: {e}")
    
    def start_scheduler(self):
        """Start the scheduler in a separate thread"""
        if self.is_running:
            logger.warning("Scheduler is already running")
            return
        
        self.is_running = True
        
        # Schedule the check function to run every minute
        schedule.every(1).minutes.do(self.check_and_execute_posts)
        
        def run_scheduler():
            logger.info("Post scheduler started")
            while self.is_running:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            logger.info("Post scheduler stopped")
        
        self.scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        self.scheduler_thread.start()
    
    def stop_scheduler(self):
        """Stop the scheduler"""
        self.is_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        logger.info("Post scheduler stopped")
    
    def get_schedule_status(self) -> Dict[str, Any]:
        """Get current schedule status"""
        try:
            total_scheduled = len([p for p in self.scheduled_posts if p['status'] == 'scheduled'])
            total_posted = len([p for p in self.scheduled_posts if p['status'] == 'posted'])
            total_failed = len([p for p in self.scheduled_posts if p['status'] == 'failed'])
            
            # Get next scheduled post
            next_post = None
            next_post_time = None
            
            scheduled_posts = [p for p in self.scheduled_posts if p['status'] == 'scheduled']
            if scheduled_posts:
                scheduled_posts.sort(key=lambda x: x['scheduled_time'])
                next_post = scheduled_posts[0]
                next_post_time = datetime.fromisoformat(next_post['scheduled_time'])
            
            return {
                'is_running': self.is_running,
                'total_scheduled': total_scheduled,
                'total_posted': total_posted,
                'total_failed': total_failed,
                'next_post_time': next_post_time.isoformat() if next_post_time else None,
                'next_post_id': next_post['id'] if next_post else None
            }
            
        except Exception as e:
            logger.error(f"Error getting schedule status: {e}")
            return {}
    
    def cleanup_old_entries(self, days_old: int = 30):
        """Clean up old schedule entries"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            original_count = len(self.scheduled_posts)
            
            self.scheduled_posts = [
                entry for entry in self.scheduled_posts
                if datetime.fromisoformat(entry['created_at']) > cutoff_date
                or entry['status'] == 'scheduled'  # Keep all scheduled posts regardless of age
            ]
            
            removed_count = original_count - len(self.scheduled_posts)
            
            if removed_count > 0:
                self._save_schedule()
                logger.info(f"Cleaned up {removed_count} old schedule entries")
            
            return removed_count
            
        except Exception as e:
            logger.error(f"Error cleaning up old entries: {e}")
            return 0
